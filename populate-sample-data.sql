-- Populate sample data for Bus Reservation System
USE bus_reservation_system;

-- Insert sample buses
INSERT IGNORE INTO buses (bus_id, bus_number, bus_type, total_seats, amenities, status) VALUES
(1, 'MH-01-AB-1234', 'AC Sleeper', 40, 'AC, WiFi, Charging Point', 'Active'),
(2, 'MH-01-CD-5678', 'Non-AC Seater', 45, 'WiFi, Charging Point', 'Active'),
(3, 'MH-01-EF-9012', 'AC Seater', 35, 'AC, WiFi, Charging Point, Entertainment', 'Active'),
(4, 'MH-01-GH-3456', 'Volvo AC', 42, 'AC, WiFi, Charging Point, Blanket', 'Active'),
(5, 'MH-01-IJ-7890', 'Luxury Sleeper', 30, 'AC, WiFi, Charging Point, Entertainment, Blanket', 'Active');

-- Insert sample routes
INSERT IGNORE INTO routes (route_id, source, destination, distance, duration) VALUES
(1, 'Mumbai', 'Pune', 150, '3h 30m'),
(2, 'Mumbai', 'Bangalore', 980, '14h 30m'),
(3, 'Delhi', 'Mumbai', 1400, '20h 00m'),
(4, 'Pune', 'Bangalore', 850, '12h 00m'),
(5, 'Mumbai', 'Goa', 460, '8h 30m'),
(6, 'Delhi', 'Jaipur', 280, '5h 30m'),
(7, 'Chennai', 'Bangalore', 350, '6h 30m'),
(8, 'Hyderabad', 'Mumbai', 710, '12h 30m');

-- Insert sample drivers
INSERT IGNORE INTO drivers (driver_id, full_name, phone, license_number, experience_years) VALUES
(1, 'Rajesh Kumar', '**********', 'DL123456789', 8),
(2, 'Amit Sharma', '**********', 'DL123456790', 12),
(3, 'Suresh Patel', '**********', 'DL123456791', 6),
(4, 'Vikram Singh', '**********', 'DL123456792', 10),
(5, 'Ravi Gupta', '**********', 'DL123456793', 7);

-- Insert sample bus schedules for the next 30 days
INSERT IGNORE INTO bus_schedules (schedule_id, bus_id, route_id, driver_id, departure_time, arrival_time, fare, available_seats, total_seats) VALUES
-- Mumbai to Pune routes
(1, 1, 1, 1, '2024-12-25 06:00:00', '2024-12-25 09:30:00', 800, 35, 40),
(2, 2, 1, 2, '2024-12-25 08:00:00', '2024-12-25 11:30:00', 600, 40, 45),
(3, 3, 1, 3, '2024-12-25 14:00:00', '2024-12-25 17:30:00', 750, 30, 35),
(4, 4, 1, 4, '2024-12-25 20:00:00', '2024-12-25 23:30:00', 900, 38, 42),

-- Mumbai to Bangalore routes
(5, 1, 2, 1, '2024-12-26 18:00:00', '2024-12-27 08:30:00', 1500, 35, 40),
(6, 5, 2, 5, '2024-12-26 20:00:00', '2024-12-27 10:30:00', 2200, 25, 30),

-- Delhi to Mumbai routes
(7, 3, 3, 3, '2024-12-25 16:00:00', '2024-12-26 12:00:00', 2000, 30, 35),
(8, 4, 3, 4, '2024-12-25 22:00:00', '2024-12-26 18:00:00', 2500, 40, 42),

-- Mumbai to Goa routes
(9, 2, 5, 2, '2024-12-27 07:00:00', '2024-12-27 15:30:00', 1200, 42, 45),
(10, 5, 5, 5, '2024-12-27 21:00:00', '2024-12-28 05:30:00', 1800, 28, 30),

-- Additional schedules for next few days
(11, 1, 1, 1, '2024-12-26 06:00:00', '2024-12-26 09:30:00', 800, 35, 40),
(12, 2, 1, 2, '2024-12-26 08:00:00', '2024-12-26 11:30:00', 600, 40, 45),
(13, 3, 1, 3, '2024-12-26 14:00:00', '2024-12-26 17:30:00', 750, 30, 35),
(14, 4, 1, 4, '2024-12-26 20:00:00', '2024-12-26 23:30:00', 900, 38, 42),

(15, 1, 1, 1, '2024-12-27 06:00:00', '2024-12-27 09:30:00', 800, 35, 40),
(16, 2, 1, 2, '2024-12-27 08:00:00', '2024-12-27 11:30:00', 600, 40, 45),
(17, 3, 1, 3, '2024-12-27 14:00:00', '2024-12-27 17:30:00', 750, 30, 35),
(18, 4, 1, 4, '2024-12-27 20:00:00', '2024-12-27 23:30:00', 900, 38, 42);

-- Update schedules for current date and future dates
UPDATE bus_schedules SET 
  departure_time = DATE_ADD(CURDATE(), INTERVAL HOUR(departure_time) HOUR) + INTERVAL MINUTE(departure_time) MINUTE,
  arrival_time = DATE_ADD(CURDATE(), INTERVAL HOUR(arrival_time) HOUR) + INTERVAL MINUTE(arrival_time) MINUTE
WHERE DATE(departure_time) < CURDATE();

-- Add more schedules for today and tomorrow
INSERT IGNORE INTO bus_schedules (bus_id, route_id, driver_id, departure_time, arrival_time, fare, available_seats, total_seats) VALUES
-- Today's schedules
(1, 1, 1, CONCAT(CURDATE(), ' 06:00:00'), CONCAT(CURDATE(), ' 09:30:00'), 800, 35, 40),
(2, 1, 2, CONCAT(CURDATE(), ' 08:00:00'), CONCAT(CURDATE(), ' 11:30:00'), 600, 40, 45),
(3, 1, 3, CONCAT(CURDATE(), ' 14:00:00'), CONCAT(CURDATE(), ' 17:30:00'), 750, 30, 35),
(4, 1, 4, CONCAT(CURDATE(), ' 20:00:00'), CONCAT(CURDATE(), ' 23:30:00'), 900, 38, 42),

-- Tomorrow's schedules
(1, 1, 1, CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 06:00:00'), CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 09:30:00'), 800, 35, 40),
(2, 1, 2, CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 08:00:00'), CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 11:30:00'), 600, 40, 45),
(3, 1, 3, CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 14:00:00'), CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 17:30:00'), 750, 30, 35),
(4, 1, 4, CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 20:00:00'), CONCAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), ' 23:30:00'), 900, 38, 42);

-- Show final counts
SELECT 'DATA POPULATED SUCCESSFULLY' as status;
SELECT COUNT(*) as total_buses FROM buses;
SELECT COUNT(*) as total_routes FROM routes;
SELECT COUNT(*) as total_drivers FROM drivers;
SELECT COUNT(*) as total_schedules FROM bus_schedules;
