@echo off
echo ========================================
echo   Bus Reservation System - Fix All Issues
echo ========================================
echo.

echo 1. Running database migrations...
mysql -u root -p bus_reservation_system < fix-database.sql
if %errorlevel% neq 0 (
    echo ERROR: Database migration failed!
    pause
    exit /b 1
)
echo Database migrations completed successfully!
echo.

echo 2. Installing backend dependencies...
cd backend
call npm install
if %errorlevel% neq 0 (
    echo ERROR: Backend dependency installation failed!
    pause
    exit /b 1
)
echo Backend dependencies installed successfully!
echo.

echo 3. Installing frontend dependencies...
cd ../frontend
call npm install
if %errorlevel% neq 0 (
    echo ERROR: Frontend dependency installation failed!
    pause
    exit /b 1
)
echo Frontend dependencies installed successfully!
echo.

echo 4. Building frontend...
call npm run build
if %errorlevel% neq 0 (
    echo ERROR: Frontend build failed!
    pause
    exit /b 1
)
echo Frontend built successfully!
echo.

echo ========================================
echo   All fixes applied successfully!
echo ========================================
echo.
echo Next steps:
echo 1. Start backend: cd backend && npm start
echo 2. Start frontend: cd frontend && npm run dev
echo 3. Access admin dashboard at: http://localhost:3000/admin
echo 4. Test all functionality as per TESTING_GUIDE.md
echo.
pause
