-- Check database content for Bus Reservation System
USE bus_reservation_system;

-- Show all tables
SHOW TABLES;

-- Check buses table
SELECT 'BUSES TABLE:' as info;
SELECT COUNT(*) as total_buses FROM buses;
SELECT * FROM buses LIMIT 5;

-- Check routes table
SELECT 'ROUTES TABLE:' as info;
SELECT COUNT(*) as total_routes FROM routes;
SELECT * FROM routes LIMIT 5;

-- Check bus_schedules table
SELECT 'BUS_SCHEDULES TABLE:' as info;
SELECT COUNT(*) as total_schedules FROM bus_schedules;
SELECT * FROM bus_schedules LIMIT 5;

-- Check drivers table
SELECT 'DRIVERS TABLE:' as info;
SELECT COUNT(*) as total_drivers FROM drivers;
SELECT * FROM drivers LIMIT 5;
