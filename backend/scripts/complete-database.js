const db = require('../db/connection');

async function completeDatabase() {
  try {
    console.log('🔧 Completing database setup...\n');
    
    // Drop remaining problematic table
    try {
      await db.execute('DROP TABLE IF EXISTS drivers');
      console.log('✅ Dropped drivers table');
    } catch (error) {
      console.log('⚠️ Drivers table already handled');
    }
    
    // Create drivers table
    await db.execute(`
      CREATE TABLE IF NOT EXISTS drivers (
        driver_id INT AUTO_INCREMENT PRIMARY KEY,
        full_name VARCHAR(100) NOT NULL,
        license_number VARCHAR(50) UNIQUE NOT NULL,
        phone VARCHAR(15) NOT NULL,
        experience_years INT DEFAULT 0,
        status ENUM('Active', 'Inactive') DEFAULT 'Active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);
    console.log('✅ Drivers table created');
    
    // Add sample data for testing
    console.log('\n📊 Adding sample data...');
    
    // Add sample bookings
    const [users] = await db.execute('SELECT user_id FROM users LIMIT 3');
    const [schedules] = await db.execute('SELECT schedule_id FROM bus_schedules LIMIT 3');
    
    if (users.length > 0 && schedules.length > 0) {
      for (let i = 0; i < Math.min(users.length, schedules.length); i++) {
        const user = users[i];
        const schedule = schedules[i];
        
        // Create booking
        const [bookingResult] = await db.execute(`
          INSERT INTO bookings (user_id, schedule_id, journey_date, seat_numbers, total_seats, total_fare, booking_status, payment_status)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          user.user_id,
          schedule.schedule_id,
          '2025-06-20',
          `A${i+1},A${i+2}`,
          2,
          500.00,
          'Confirmed',
          'Completed'
        ]);
        
        const bookingId = bookingResult.insertId;
        
        // Create payment
        await db.execute(`
          INSERT INTO payments (booking_id, user_id, amount, payment_method, payment_status, transaction_id)
          VALUES (?, ?, ?, ?, ?, ?)
        `, [
          bookingId,
          user.user_id,
          500.00,
          'Credit Card',
          'Completed',
          `TXN_${bookingId}_${Date.now()}`
        ]);
        
        // Create booking history
        await db.execute(`
          INSERT INTO booking_history (booking_id, user_id, action, details)
          VALUES (?, ?, ?, ?)
        `, [
          bookingId,
          user.user_id,
          'Booking Created',
          'Booking created and payment completed successfully'
        ]);
        
        console.log(`   ✅ Created sample booking ${bookingId} for user ${user.user_id}`);
      }
    }
    
    // Add sample drivers
    const sampleDrivers = [
      { name: 'Rajesh Kumar', license: 'DL001', phone: '**********', experience: 5 },
      { name: 'Suresh Sharma', license: 'DL002', phone: '**********', experience: 8 },
      { name: 'Mahesh Patel', license: 'DL003', phone: '**********', experience: 3 }
    ];
    
    for (const driver of sampleDrivers) {
      await db.execute(`
        INSERT INTO drivers (full_name, license_number, phone, experience_years)
        VALUES (?, ?, ?, ?)
      `, [driver.name, driver.license, driver.phone, driver.experience]);
    }
    console.log(`   ✅ Added ${sampleDrivers.length} sample drivers`);
    
    // Verify final database state
    console.log('\n📋 Final database verification:');
    
    const [tables] = await db.execute('SHOW TABLES');
    console.log(`   Total tables: ${tables.length}`);
    
    // Count records in each table
    const tableCounts = {};
    for (const table of tables) {
      const tableName = Object.values(table)[0];
      try {
        const [count] = await db.execute(`SELECT COUNT(*) as count FROM ${tableName}`);
        tableCounts[tableName] = count[0].count;
      } catch (error) {
        tableCounts[tableName] = 'Error';
      }
    }
    
    console.log('\n📊 Record counts:');
    Object.entries(tableCounts).forEach(([table, count]) => {
      console.log(`   ${table}: ${count}`);
    });
    
    // Test booking functionality
    console.log('\n🧪 Testing booking functionality...');
    const [recentBookings] = await db.execute(`
      SELECT b.booking_id, b.user_id, b.total_fare, b.payment_status,
             u.full_name, p.payment_status as payment_table_status
      FROM bookings b
      JOIN users u ON b.user_id = u.user_id
      LEFT JOIN payments p ON b.booking_id = p.booking_id
      ORDER BY b.booking_date DESC
      LIMIT 3
    `);
    
    console.log('   Recent bookings:');
    recentBookings.forEach(booking => {
      console.log(`     - Booking ${booking.booking_id}: ${booking.full_name}, ₹${booking.total_fare}, Status: ${booking.payment_status}`);
    });
    
    console.log('\n✅ Database setup completed successfully!');
    console.log('🎉 Ready for booking and payment functionality!');
    
  } catch (error) {
    console.error('❌ Error completing database setup:', error);
  } finally {
    process.exit(0);
  }
}

completeDatabase();
