const db = require('../db/connection');

async function deleteSupriyaUsers() {
  try {
    console.log('🔍 Searching for users named "Su<PERSON><PERSON>"...');
    
    // Find all users with name "Su<PERSON>riya" (case-insensitive)
    const [supriyaUsers] = await db.execute(`
      SELECT user_id, full_name, email, phone, created_at,
             (SELECT COUNT(*) FROM bookings WHERE bookings.user_id = users.user_id) as total_bookings,
             (SELECT COUNT(*) FROM feedback WHERE feedback.user_id = users.user_id) as total_feedback
      FROM users 
      WHERE LOWER(full_name) LIKE LOWER('%supriya%')
    `);

    if (supriyaUsers.length === 0) {
      console.log('❌ No users named "Supriya" found in the database.');
      return;
    }

    console.log(`📋 Found ${supriyaUsers.length} user(s) named "Supriya":`);
    supriyaUsers.forEach((user, index) => {
      console.log(`${index + 1}. ID: ${user.user_id}, Name: ${user.full_name}, Email: ${user.email}`);
      console.log(`   📊 Bookings: ${user.total_bookings}, Feedback: ${user.total_feedback}`);
    });

    console.log('\n🗑️ Starting deletion process...');

    let totalDeletedBookings = 0;
    let totalDeletedFeedback = 0;
    let deletedUsers = [];

    // Delete each user and their associated data
    for (const user of supriyaUsers) {
      const connection = await db.getConnection();
      
      try {
        console.log(`\n🔄 Processing user: ${user.full_name} (ID: ${user.user_id})`);
        
        // Start transaction
        await connection.beginTransaction();

        // Count data to be deleted for this user
        const [bookingCount] = await connection.execute(
          'SELECT COUNT(*) as count FROM bookings WHERE user_id = ?',
          [user.user_id]
        );

        const [feedbackCount] = await connection.execute(
          'SELECT COUNT(*) as count FROM feedback WHERE user_id = ?',
          [user.user_id]
        );

        console.log(`   📊 Will delete: ${bookingCount[0].count} bookings, ${feedbackCount[0].count} feedback`);

        // Delete feedback related to user's bookings
        await connection.execute(`
          DELETE f FROM feedback f 
          INNER JOIN bookings b ON f.booking_id = b.booking_id 
          WHERE b.user_id = ?
        `, [user.user_id]);

        // Delete feedback directly linked to user
        await connection.execute('DELETE FROM feedback WHERE user_id = ?', [user.user_id]);

        // Delete payments related to user's bookings (if table exists)
        try {
          await connection.execute(`
            DELETE p FROM payments p 
            INNER JOIN bookings b ON p.booking_id = b.booking_id 
            WHERE b.user_id = ?
          `, [user.user_id]);
        } catch (paymentError) {
          console.log('   ℹ️ Payments table not found, skipping payment deletion');
        }

        // Delete user's bookings
        await connection.execute('DELETE FROM bookings WHERE user_id = ?', [user.user_id]);

        // Delete the user
        await connection.execute('DELETE FROM users WHERE user_id = ?', [user.user_id]);

        // Commit transaction
        await connection.commit();

        console.log(`   ✅ Successfully deleted user: ${user.full_name}`);
        
        totalDeletedBookings += bookingCount[0].count;
        totalDeletedFeedback += feedbackCount[0].count;
        deletedUsers.push({
          id: user.user_id,
          name: user.full_name,
          email: user.email,
          deletedBookings: bookingCount[0].count,
          deletedFeedback: feedbackCount[0].count
        });

      } catch (error) {
        // Rollback transaction on error
        await connection.rollback();
        console.error(`   ❌ Error deleting user ${user.full_name}:`, error.message);
      } finally {
        // Release connection
        connection.release();
      }
    }

    // Summary
    console.log('\n📊 DELETION SUMMARY:');
    console.log('='.repeat(50));
    console.log(`👥 Total users deleted: ${deletedUsers.length}`);
    console.log(`📅 Total bookings deleted: ${totalDeletedBookings}`);
    console.log(`💬 Total feedback deleted: ${totalDeletedFeedback}`);
    
    console.log('\n📋 Deleted users details:');
    deletedUsers.forEach((user, index) => {
      console.log(`${index + 1}. ${user.name} (${user.email})`);
      console.log(`   📊 Deleted: ${user.deletedBookings} bookings, ${user.deletedFeedback} feedback`);
    });

    console.log('\n✅ All "Supriya" users and their associated data have been successfully deleted from the database!');

  } catch (error) {
    console.error('❌ Error in deletion process:', error);
  } finally {
    process.exit(0);
  }
}

// Run the deletion
deleteSupriyaUsers();
