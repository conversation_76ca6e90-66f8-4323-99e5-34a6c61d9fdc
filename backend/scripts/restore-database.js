const db = require('../db/connection');
const fs = require('fs');
const path = require('path');

async function restoreDatabase() {
  try {
    console.log('🔧 Restoring database from SQL files...\n');
    
    // Read and execute init.sql
    console.log('1. Creating database schema...');
    const initSQL = fs.readFileSync(path.join(__dirname, '../db/init.sql'), 'utf8');
    
    // Split SQL statements and execute them
    const statements = initSQL.split(';').filter(stmt => stmt.trim().length > 0);
    
    for (const statement of statements) {
      try {
        await db.execute(statement);
      } catch (error) {
        if (!error.message.includes('already exists')) {
          console.log(`⚠️ Warning: ${error.message}`);
        }
      }
    }
    console.log('✅ Database schema created');
    
    // Read and execute sample-data.sql
    console.log('2. Loading sample data...');
    const sampleSQL = fs.readFileSync(path.join(__dirname, '../db/sample-data.sql'), 'utf8');
    
    // Split SQL statements and execute them
    const dataStatements = sampleSQL.split(';').filter(stmt => stmt.trim().length > 0);
    
    for (const statement of dataStatements) {
      try {
        await db.execute(statement);
      } catch (error) {
        if (!error.message.includes('Duplicate entry')) {
          console.log(`⚠️ Warning: ${error.message}`);
        }
      }
    }
    console.log('✅ Sample data loaded');
    
    // Add missing tables for booking system
    console.log('3. Adding booking system tables...');
    
    // Payments table
    await db.execute(`
      CREATE TABLE IF NOT EXISTS payments (
        payment_id INT AUTO_INCREMENT PRIMARY KEY,
        booking_id INT NOT NULL,
        user_id INT NOT NULL,
        amount DECIMAL(10,2) NOT NULL,
        payment_method ENUM('Credit Card', 'Debit Card', 'PayPal', 'Digital Wallet') NOT NULL,
        payment_status ENUM('Pending', 'Completed', 'Failed', 'Refunded') DEFAULT 'Pending',
        transaction_id VARCHAR(100),
        payment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        refund_amount DECIMAL(10,2) DEFAULT 0,
        refund_date TIMESTAMP NULL,
        FOREIGN KEY (booking_id) REFERENCES bookings(booking_id) ON DELETE CASCADE,
        FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
      )
    `);
    console.log('   ✅ Payments table created');
    
    // Booking history table
    await db.execute(`
      CREATE TABLE IF NOT EXISTS booking_history (
        history_id INT AUTO_INCREMENT PRIMARY KEY,
        booking_id INT NOT NULL,
        user_id INT NOT NULL,
        action VARCHAR(50) NOT NULL,
        action_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        details TEXT,
        FOREIGN KEY (booking_id) REFERENCES bookings(booking_id) ON DELETE CASCADE,
        FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
      )
    `);
    console.log('   ✅ Booking history table created');
    
    // Add payment_status column to bookings if not exists
    try {
      await db.execute(`
        ALTER TABLE bookings 
        ADD COLUMN payment_status ENUM('Pending', 'Completed', 'Failed') DEFAULT 'Pending'
      `);
      console.log('   ✅ Payment status column added to bookings');
    } catch (error) {
      if (error.message.includes('Duplicate column')) {
        console.log('   ✅ Payment status column already exists');
      } else {
        console.log('   ⚠️ Could not add payment status column:', error.message);
      }
    }
    
    // Create sample payments for existing bookings
    console.log('4. Creating sample payment data...');
    const [existingBookings] = await db.execute('SELECT booking_id, user_id, total_fare FROM bookings LIMIT 5');
    
    for (const booking of existingBookings) {
      try {
        // Create payment record
        await db.execute(`
          INSERT INTO payments (booking_id, user_id, amount, payment_method, payment_status, transaction_id)
          VALUES (?, ?, ?, ?, ?, ?)
        `, [
          booking.booking_id,
          booking.user_id,
          booking.total_fare,
          'Credit Card',
          'Completed',
          `TXN_${booking.booking_id}_${Date.now()}`
        ]);
        
        // Update booking payment status
        await db.execute(
          'UPDATE bookings SET payment_status = ? WHERE booking_id = ?',
          ['Completed', booking.booking_id]
        );
        
        // Create booking history
        await db.execute(`
          INSERT INTO booking_history (booking_id, user_id, action, details)
          VALUES (?, ?, ?, ?)
        `, [
          booking.booking_id,
          booking.user_id,
          'Payment Completed',
          `Payment of ₹${booking.total_fare} completed successfully via Credit Card`
        ]);
        
        console.log(`   ✅ Payment created for booking ${booking.booking_id}`);
      } catch (error) {
        if (!error.message.includes('Duplicate entry')) {
          console.log(`   ⚠️ Could not create payment for booking ${booking.booking_id}: ${error.message}`);
        }
      }
    }
    
    // Verify database state
    console.log('\n5. Verifying database state...');
    
    const [tables] = await db.execute('SHOW TABLES');
    console.log(`   Total tables: ${tables.length}`);
    
    // Count records
    const counts = {};
    for (const table of tables) {
      const tableName = Object.values(table)[0];
      try {
        const [count] = await db.execute(`SELECT COUNT(*) as count FROM ${tableName}`);
        counts[tableName] = count[0].count;
      } catch (error) {
        counts[tableName] = 'Error';
      }
    }
    
    console.log('\n📊 Database contents:');
    Object.entries(counts).forEach(([table, count]) => {
      console.log(`   ${table}: ${count} records`);
    });
    
    // Test booking functionality
    console.log('\n🧪 Testing booking system...');
    const [testBookings] = await db.execute(`
      SELECT b.booking_id, b.user_id, b.total_fare, b.payment_status,
             u.full_name, p.payment_status as payment_table_status
      FROM bookings b
      JOIN users u ON b.user_id = u.user_id
      LEFT JOIN payments p ON b.booking_id = p.booking_id
      ORDER BY b.booking_date DESC
      LIMIT 3
    `);
    
    console.log('   Recent bookings with payments:');
    testBookings.forEach(booking => {
      console.log(`     - Booking ${booking.booking_id}: ${booking.full_name}`);
      console.log(`       Amount: ₹${booking.total_fare}, Status: ${booking.payment_status}`);
    });
    
    console.log('\n✅ Database restoration completed successfully!');
    console.log('🎉 ZenBus booking system is ready!');
    
  } catch (error) {
    console.error('❌ Error restoring database:', error);
  } finally {
    process.exit(0);
  }
}

restoreDatabase();
