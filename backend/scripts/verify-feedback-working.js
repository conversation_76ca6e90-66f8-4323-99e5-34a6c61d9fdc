const db = require('../db/connection');

async function verifyFeedbackWorking() {
  try {
    console.log('✅ Verifying feedback functionality...\n');
    
    // Check the latest feedback
    const [latestFeedback] = await db.execute(`
      SELECT f.feedback_id, f.user_id, f.booking_id, f.rating, f.comments, f.feedback_date,
             u.full_name, u.email
      FROM feedback f
      JOIN users u ON f.user_id = u.user_id
      ORDER BY f.feedback_date DESC
      LIMIT 5
    `);
    
    console.log('📋 Latest feedback entries:');
    latestFeedback.forEach((feedback, index) => {
      console.log(`${index + 1}. ${feedback.full_name} (${feedback.email})`);
      console.log(`   Rating: ${feedback.rating}⭐`);
      console.log(`   Comments: "${feedback.comments}"`);
      console.log(`   Date: ${feedback.feedback_date}`);
      console.log(`   Booking ID: ${feedback.booking_id}`);
      console.log('');
    });
    
    // Check total feedback count
    const [totalCount] = await db.execute('SELECT COUNT(*) as count FROM feedback');
    console.log(`📊 Total feedback entries in database: ${totalCount[0].count}`);
    
    // Check feedback by rating
    const [ratingStats] = await db.execute(`
      SELECT rating, COUNT(*) as count
      FROM feedback
      GROUP BY rating
      ORDER BY rating DESC
    `);
    
    console.log('\n⭐ Feedback by rating:');
    ratingStats.forEach(stat => {
      console.log(`   ${stat.rating}⭐: ${stat.count} reviews`);
    });
    
    // Check users with feedback
    const [userStats] = await db.execute(`
      SELECT u.full_name, COUNT(f.feedback_id) as feedback_count
      FROM users u
      LEFT JOIN feedback f ON u.user_id = f.user_id
      WHERE f.feedback_id IS NOT NULL
      GROUP BY u.user_id, u.full_name
      ORDER BY feedback_count DESC
    `);
    
    console.log('\n👥 Users who have given feedback:');
    userStats.forEach(user => {
      console.log(`   ${user.full_name}: ${user.feedback_count} feedback(s)`);
    });
    
    console.log('\n✅ Feedback functionality is working perfectly!');
    console.log('🎉 Users can now submit feedback and it\'s being stored in the database!');
    
  } catch (error) {
    console.error('❌ Error verifying feedback:', error);
  } finally {
    process.exit(0);
  }
}

verifyFeedbackWorking();
