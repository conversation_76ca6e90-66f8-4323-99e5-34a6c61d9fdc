const db = require('../db/connection');

async function createMissingTables() {
  try {
    console.log('🔧 Creating missing tables for booking system...\n');
    
    // Create booking_history table
    console.log('1. Creating booking_history table...');
    await db.execute(`
      CREATE TABLE IF NOT EXISTS booking_history (
        history_id INT AUTO_INCREMENT PRIMARY KEY,
        booking_id INT NOT NULL,
        user_id INT NOT NULL,
        action VARCHAR(50) NOT NULL,
        action_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        details TEXT,
        FOREIGN KEY (booking_id) REFERENCES bookings(booking_id) ON DELETE CASCADE,
        FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
      )
    `);
    console.log('✅ Booking history table created');
    
    // Create payments table
    console.log('2. Creating payments table...');
    await db.execute(`
      CREATE TABLE IF NOT EXISTS payments (
        payment_id INT AUTO_INCREMENT PRIMARY KEY,
        booking_id INT NOT NULL,
        user_id INT NOT NULL,
        amount DECIMAL(10,2) NOT NULL,
        payment_method ENUM('Credit Card', 'Debit Card', 'PayPal', 'Digital Wallet') NOT NULL,
        payment_status ENUM('Pending', 'Completed', 'Failed', 'Refunded') DEFAULT 'Pending',
        transaction_id VARCHAR(100),
        payment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        refund_amount DECIMAL(10,2) DEFAULT 0,
        refund_date TIMESTAMP NULL,
        FOREIGN KEY (booking_id) REFERENCES bookings(booking_id) ON DELETE CASCADE,
        FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
      )
    `);
    console.log('✅ Payments table created');
    
    // Check current bookings structure
    console.log('\n3. Checking bookings table structure...');
    const [columns] = await db.execute(`
      SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_NAME = 'bookings'
      ORDER BY ORDINAL_POSITION
    `);
    
    console.log('Current bookings table columns:');
    columns.forEach(col => {
      console.log(`   - ${col.COLUMN_NAME}: ${col.DATA_TYPE} (${col.IS_NULLABLE === 'YES' ? 'NULL' : 'NOT NULL'})`);
    });
    
    // Add payment_status column to bookings if it doesn't exist
    const hasPaymentStatus = columns.some(col => col.COLUMN_NAME === 'payment_status');
    if (!hasPaymentStatus) {
      console.log('\n4. Adding payment_status to bookings table...');
      await db.execute(`
        ALTER TABLE bookings 
        ADD COLUMN payment_status ENUM('Pending', 'Completed', 'Failed') DEFAULT 'Pending'
      `);
      console.log('✅ Payment status column added to bookings');
    } else {
      console.log('\n4. Payment status column already exists in bookings table');
    }
    
    // Test the tables
    console.log('\n5. Testing table functionality...');
    
    // Get a test booking
    const [testBookings] = await db.execute('SELECT booking_id, user_id, total_fare FROM bookings LIMIT 1');
    
    if (testBookings.length > 0) {
      const testBooking = testBookings[0];
      console.log(`   Using test booking: ${testBooking.booking_id} for user ${testBooking.user_id}`);
      
      // Test payment insertion
      const [paymentResult] = await db.execute(`
        INSERT INTO payments (booking_id, user_id, amount, payment_method, payment_status, transaction_id)
        VALUES (?, ?, ?, ?, ?, ?)
      `, [testBooking.booking_id, testBooking.user_id, testBooking.total_fare, 'Credit Card', 'Completed', 'TEST_TXN_123']);
      
      console.log(`✅ Test payment inserted with ID: ${paymentResult.insertId}`);
      
      // Test booking history insertion
      const [historyResult] = await db.execute(`
        INSERT INTO booking_history (booking_id, user_id, action, details)
        VALUES (?, ?, ?, ?)
      `, [testBooking.booking_id, testBooking.user_id, 'Payment Completed', 'Test payment completed successfully']);
      
      console.log(`✅ Test booking history inserted with ID: ${historyResult.insertId}`);
      
      // Clean up test data
      await db.execute('DELETE FROM payments WHERE payment_id = ?', [paymentResult.insertId]);
      await db.execute('DELETE FROM booking_history WHERE history_id = ?', [historyResult.insertId]);
      console.log('✅ Test data cleaned up');
    }
    
    // Show final table count
    console.log('\n6. Final database status:');
    const [tables] = await db.execute('SHOW TABLES');
    console.log(`   Total tables: ${tables.length}`);
    tables.forEach((table, index) => {
      console.log(`   ${index + 1}. ${Object.values(table)[0]}`);
    });
    
    console.log('\n✅ Missing tables created successfully!');
    
  } catch (error) {
    console.error('❌ Error creating missing tables:', error);
  } finally {
    process.exit(0);
  }
}

createMissingTables();
