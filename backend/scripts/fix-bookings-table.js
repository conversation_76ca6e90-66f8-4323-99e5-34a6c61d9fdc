const db = require('../db/connection');

async function fixBookingsTable() {
  try {
    console.log('🔧 Fixing bookings table structure...\n');
    
    // First, backup existing booking data
    console.log('1. Backing up existing booking data...');
    const [existingBookings] = await db.execute(`
      SELECT DISTINCT booking_id, user_id, schedule_id, journey_date, seat_numbers, 
             total_seats, total_fare, passenger_details, booking_status, booking_date
      FROM bookings 
      WHERE booking_id IS NOT NULL
    `);
    
    console.log(`   Found ${existingBookings.length} bookings to preserve`);
    
    // Drop and recreate bookings table with correct structure
    console.log('2. Recreating bookings table...');
    await db.execute('DROP TABLE IF EXISTS booking_history');
    await db.execute('DROP TABLE IF EXISTS payments');
    await db.execute('DROP TABLE IF EXISTS feedback');
    await db.execute('DROP TABLE IF EXISTS bookings');
    
    // Create bookings table with proper structure
    await db.execute(`
      CREATE TABLE bookings (
        booking_id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        schedule_id INT NOT NULL,
        journey_date DATE NOT NULL,
        seat_numbers VARCHAR(255) NOT NULL,
        total_seats INT NOT NULL,
        total_fare DECIMAL(10,2) NOT NULL,
        passenger_details JSON,
        booking_status ENUM('Confirmed', 'Cancelled', 'Completed') DEFAULT 'Confirmed',
        payment_status ENUM('Pending', 'Completed', 'Failed') DEFAULT 'Pending',
        booking_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
        FOREIGN KEY (schedule_id) REFERENCES bus_schedules(schedule_id) ON DELETE CASCADE
      )
    `);
    console.log('✅ Bookings table recreated');
    
    // Recreate payments table
    await db.execute(`
      CREATE TABLE payments (
        payment_id INT AUTO_INCREMENT PRIMARY KEY,
        booking_id INT NOT NULL,
        user_id INT NOT NULL,
        amount DECIMAL(10,2) NOT NULL,
        payment_method ENUM('Credit Card', 'Debit Card', 'PayPal', 'Digital Wallet') NOT NULL,
        payment_status ENUM('Pending', 'Completed', 'Failed', 'Refunded') DEFAULT 'Pending',
        transaction_id VARCHAR(100),
        payment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        refund_amount DECIMAL(10,2) DEFAULT 0,
        refund_date TIMESTAMP NULL,
        FOREIGN KEY (booking_id) REFERENCES bookings(booking_id) ON DELETE CASCADE,
        FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
      )
    `);
    console.log('✅ Payments table recreated');
    
    // Recreate booking_history table
    await db.execute(`
      CREATE TABLE booking_history (
        history_id INT AUTO_INCREMENT PRIMARY KEY,
        booking_id INT NOT NULL,
        user_id INT NOT NULL,
        action VARCHAR(50) NOT NULL,
        action_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        details TEXT,
        FOREIGN KEY (booking_id) REFERENCES bookings(booking_id) ON DELETE CASCADE,
        FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
      )
    `);
    console.log('✅ Booking history table recreated');
    
    // Recreate feedback table
    await db.execute(`
      CREATE TABLE feedback (
        feedback_id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        booking_id INT,
        rating INT NOT NULL CHECK (rating >= 1 AND rating <= 5),
        comments TEXT,
        feedback_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        admin_response TEXT,
        response_date TIMESTAMP NULL,
        FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
        FOREIGN KEY (booking_id) REFERENCES bookings(booking_id) ON DELETE CASCADE
      )
    `);
    console.log('✅ Feedback table recreated');
    
    // Restore booking data
    console.log('3. Restoring booking data...');
    for (const booking of existingBookings) {
      try {
        await db.execute(`
          INSERT INTO bookings (user_id, schedule_id, journey_date, seat_numbers, total_seats, total_fare, passenger_details, booking_status, booking_date)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          booking.user_id,
          booking.schedule_id,
          booking.journey_date,
          booking.seat_numbers,
          booking.total_seats,
          booking.total_fare,
          booking.passenger_details,
          booking.booking_status || 'Confirmed',
          booking.booking_date
        ]);
      } catch (error) {
        console.log(`⚠️ Skipped invalid booking:`, error.message);
      }
    }
    console.log(`✅ Restored ${existingBookings.length} bookings`);
    
    // Add some sample payments for existing bookings
    console.log('4. Adding sample payment data...');
    const [restoredBookings] = await db.execute('SELECT booking_id, user_id, total_fare FROM bookings LIMIT 5');
    
    for (const booking of restoredBookings) {
      await db.execute(`
        INSERT INTO payments (booking_id, user_id, amount, payment_method, payment_status, transaction_id)
        VALUES (?, ?, ?, ?, ?, ?)
      `, [
        booking.booking_id,
        booking.user_id,
        booking.total_fare,
        'Credit Card',
        'Completed',
        `TXN_${booking.booking_id}_${Date.now()}`
      ]);
      
      // Update booking payment status
      await db.execute(
        'UPDATE bookings SET payment_status = ? WHERE booking_id = ?',
        ['Completed', booking.booking_id]
      );
      
      // Add booking history
      await db.execute(`
        INSERT INTO booking_history (booking_id, user_id, action, details)
        VALUES (?, ?, ?, ?)
      `, [
        booking.booking_id,
        booking.user_id,
        'Payment Completed',
        `Payment of ₹${booking.total_fare} completed successfully`
      ]);
    }
    console.log('✅ Sample payment data added');
    
    // Verify final structure
    console.log('5. Verifying table structure...');
    const [bookingColumns] = await db.execute(`
      SELECT COLUMN_NAME, DATA_TYPE 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_NAME = 'bookings'
      ORDER BY ORDINAL_POSITION
    `);
    
    console.log('Final bookings table structure:');
    bookingColumns.forEach(col => {
      console.log(`   - ${col.COLUMN_NAME}: ${col.DATA_TYPE}`);
    });
    
    // Show counts
    const [bookingCount] = await db.execute('SELECT COUNT(*) as count FROM bookings');
    const [paymentCount] = await db.execute('SELECT COUNT(*) as count FROM payments');
    const [historyCount] = await db.execute('SELECT COUNT(*) as count FROM booking_history');
    
    console.log('\n📊 Final counts:');
    console.log(`   Bookings: ${bookingCount[0].count}`);
    console.log(`   Payments: ${paymentCount[0].count}`);
    console.log(`   History: ${historyCount[0].count}`);
    
    console.log('\n✅ Bookings table structure fixed successfully!');
    
  } catch (error) {
    console.error('❌ Error fixing bookings table:', error);
  } finally {
    process.exit(0);
  }
}

fixBookingsTable();
