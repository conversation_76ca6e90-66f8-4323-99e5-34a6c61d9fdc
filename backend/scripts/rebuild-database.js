const db = require('../db/connection');

async function rebuildDatabase() {
  try {
    console.log('🔧 Rebuilding database with proper structure...\n');
    
    // Backup essential data
    console.log('1. Backing up essential data...');
    
    // Backup users (excluding duplicates)
    const [users] = await db.execute(`
      SELECT DISTINCT user_id, full_name, email, password, phone, created_at
      FROM users 
      WHERE user_id IS NOT NULL AND email IS NOT NULL
    `);
    console.log(`   Users: ${users.length}`);
    
    // Backup admins
    const [admins] = await db.execute('SELECT * FROM admins');
    console.log(`   Admins: ${admins.length}`);
    
    // Backup buses
    const [buses] = await db.execute('SELECT * FROM buses');
    console.log(`   Buses: ${buses.length}`);
    
    // Backup routes
    const [routes] = await db.execute('SELECT * FROM routes');
    console.log(`   Routes: ${routes.length}`);
    
    // Backup schedules
    const [schedules] = await db.execute('SELECT * FROM bus_schedules');
    console.log(`   Schedules: ${schedules.length}`);
    
    // Backup drivers
    const [drivers] = await db.execute('SELECT * FROM drivers');
    console.log(`   Drivers: ${drivers.length}`);
    
    // Backup bus_staff
    const [busStaff] = await db.execute('SELECT * FROM bus_staff');
    console.log(`   Bus Staff: ${busStaff.length}`);
    
    // Drop all tables
    console.log('\n2. Dropping all tables...');
    const tablesToDrop = [
      'booking_history', 'payments', 'feedback', 'bookings', 
      'driver_schedules', 'maintenance', 'bus_staff', 'drivers',
      'bus_schedules', 'routes', 'buses', 'users', 'admins'
    ];
    
    for (const table of tablesToDrop) {
      try {
        await db.execute(`DROP TABLE IF EXISTS ${table}`);
        console.log(`   ✅ Dropped ${table}`);
      } catch (error) {
        console.log(`   ⚠️ Could not drop ${table}: ${error.message}`);
      }
    }
    
    // Create tables with proper structure
    console.log('\n3. Creating tables with proper structure...');
    
    // Users table
    await db.execute(`
      CREATE TABLE users (
        user_id INT AUTO_INCREMENT PRIMARY KEY,
        full_name VARCHAR(100) NOT NULL,
        email VARCHAR(100) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        phone VARCHAR(15),
        account_status ENUM('Active', 'Blocked') DEFAULT 'Active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);
    console.log('   ✅ Users table created');
    
    // Admins table
    await db.execute(`
      CREATE TABLE admins (
        admin_id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);
    console.log('   ✅ Admins table created');
    
    // Buses table
    await db.execute(`
      CREATE TABLE buses (
        bus_id INT AUTO_INCREMENT PRIMARY KEY,
        bus_number VARCHAR(20) UNIQUE NOT NULL,
        bus_type VARCHAR(50) NOT NULL,
        capacity INT NOT NULL,
        amenities TEXT,
        status ENUM('Active', 'Maintenance', 'Inactive') DEFAULT 'Active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);
    console.log('   ✅ Buses table created');
    
    // Routes table
    await db.execute(`
      CREATE TABLE routes (
        route_id INT AUTO_INCREMENT PRIMARY KEY,
        source VARCHAR(100) NOT NULL,
        destination VARCHAR(100) NOT NULL,
        distance DECIMAL(8,2),
        base_fare DECIMAL(10,2) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);
    console.log('   ✅ Routes table created');
    
    // Bus schedules table
    await db.execute(`
      CREATE TABLE bus_schedules (
        schedule_id INT AUTO_INCREMENT PRIMARY KEY,
        bus_id INT NOT NULL,
        route_id INT NOT NULL,
        departure_time TIME NOT NULL,
        arrival_time TIME NOT NULL,
        fare DECIMAL(10,2) NOT NULL,
        available_seats INT NOT NULL,
        status ENUM('Active', 'Inactive') DEFAULT 'Active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (bus_id) REFERENCES buses(bus_id) ON DELETE CASCADE,
        FOREIGN KEY (route_id) REFERENCES routes(route_id) ON DELETE CASCADE
      )
    `);
    console.log('   ✅ Bus schedules table created');
    
    // Drivers table
    await db.execute(`
      CREATE TABLE drivers (
        driver_id INT AUTO_INCREMENT PRIMARY KEY,
        full_name VARCHAR(100) NOT NULL,
        license_number VARCHAR(50) UNIQUE NOT NULL,
        phone VARCHAR(15) NOT NULL,
        experience_years INT DEFAULT 0,
        status ENUM('Active', 'Inactive') DEFAULT 'Active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);
    console.log('   ✅ Drivers table created');
    
    // Bus staff table
    await db.execute(`
      CREATE TABLE bus_staff (
        staff_id INT AUTO_INCREMENT PRIMARY KEY,
        full_name VARCHAR(100) NOT NULL,
        role ENUM('Conductor', 'Cleaner', 'Mechanic') NOT NULL,
        phone VARCHAR(15) NOT NULL,
        status ENUM('Active', 'Inactive') DEFAULT 'Active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);
    console.log('   ✅ Bus staff table created');
    
    // Bookings table
    await db.execute(`
      CREATE TABLE bookings (
        booking_id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        schedule_id INT NOT NULL,
        journey_date DATE NOT NULL,
        seat_numbers VARCHAR(255) NOT NULL,
        total_seats INT NOT NULL,
        total_fare DECIMAL(10,2) NOT NULL,
        passenger_details JSON,
        booking_status ENUM('Confirmed', 'Cancelled', 'Completed') DEFAULT 'Confirmed',
        payment_status ENUM('Pending', 'Completed', 'Failed') DEFAULT 'Pending',
        booking_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
        FOREIGN KEY (schedule_id) REFERENCES bus_schedules(schedule_id) ON DELETE CASCADE
      )
    `);
    console.log('   ✅ Bookings table created');
    
    // Payments table
    await db.execute(`
      CREATE TABLE payments (
        payment_id INT AUTO_INCREMENT PRIMARY KEY,
        booking_id INT NOT NULL,
        user_id INT NOT NULL,
        amount DECIMAL(10,2) NOT NULL,
        payment_method ENUM('Credit Card', 'Debit Card', 'PayPal', 'Digital Wallet') NOT NULL,
        payment_status ENUM('Pending', 'Completed', 'Failed', 'Refunded') DEFAULT 'Pending',
        transaction_id VARCHAR(100),
        payment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        refund_amount DECIMAL(10,2) DEFAULT 0,
        refund_date TIMESTAMP NULL,
        FOREIGN KEY (booking_id) REFERENCES bookings(booking_id) ON DELETE CASCADE,
        FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
      )
    `);
    console.log('   ✅ Payments table created');
    
    // Booking history table
    await db.execute(`
      CREATE TABLE booking_history (
        history_id INT AUTO_INCREMENT PRIMARY KEY,
        booking_id INT NOT NULL,
        user_id INT NOT NULL,
        action VARCHAR(50) NOT NULL,
        action_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        details TEXT,
        FOREIGN KEY (booking_id) REFERENCES bookings(booking_id) ON DELETE CASCADE,
        FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
      )
    `);
    console.log('   ✅ Booking history table created');
    
    // Feedback table
    await db.execute(`
      CREATE TABLE feedback (
        feedback_id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        booking_id INT,
        rating INT NOT NULL CHECK (rating >= 1 AND rating <= 5),
        comments TEXT,
        feedback_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        admin_response TEXT,
        response_date TIMESTAMP NULL,
        FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
        FOREIGN KEY (booking_id) REFERENCES bookings(booking_id) ON DELETE CASCADE
      )
    `);
    console.log('   ✅ Feedback table created');
    
    // Maintenance table
    await db.execute(`
      CREATE TABLE maintenance (
        maintenance_id INT AUTO_INCREMENT PRIMARY KEY,
        bus_id INT NOT NULL,
        maintenance_type VARCHAR(100) NOT NULL,
        description TEXT,
        scheduled_date DATE NOT NULL,
        completion_date DATE,
        cost DECIMAL(10,2),
        status ENUM('Scheduled', 'In Progress', 'Completed', 'Cancelled') DEFAULT 'Scheduled',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (bus_id) REFERENCES buses(bus_id) ON DELETE CASCADE
      )
    `);
    console.log('   ✅ Maintenance table created');
    
    // Driver schedules table
    await db.execute(`
      CREATE TABLE driver_schedules (
        assignment_id INT AUTO_INCREMENT PRIMARY KEY,
        driver_id INT NOT NULL,
        schedule_id INT NOT NULL,
        assigned_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (driver_id) REFERENCES drivers(driver_id) ON DELETE CASCADE,
        FOREIGN KEY (schedule_id) REFERENCES bus_schedules(schedule_id) ON DELETE CASCADE,
        UNIQUE KEY unique_driver_schedule (driver_id, schedule_id)
      )
    `);
    console.log('   ✅ Driver schedules table created');
    
    console.log('\n4. Restoring data...');
    
    // Restore users
    for (const user of users) {
      await db.execute(`
        INSERT INTO users (user_id, full_name, email, password, phone, created_at)
        VALUES (?, ?, ?, ?, ?, ?)
      `, [user.user_id, user.full_name, user.email, user.password, user.phone, user.created_at]);
    }
    console.log(`   ✅ Restored ${users.length} users`);
    
    // Restore admins
    for (const admin of admins) {
      await db.execute(`
        INSERT INTO admins (admin_id, username, password, created_at)
        VALUES (?, ?, ?, ?)
      `, [admin.admin_id, admin.username, admin.password, admin.created_at]);
    }
    console.log(`   ✅ Restored ${admins.length} admins`);
    
    // Restore buses
    for (const bus of buses) {
      await db.execute(`
        INSERT INTO buses (bus_id, bus_number, bus_type, capacity, amenities, status, created_at)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `, [bus.bus_id, bus.bus_number, bus.bus_type, bus.capacity, bus.amenities, bus.status, bus.created_at]);
    }
    console.log(`   ✅ Restored ${buses.length} buses`);
    
    // Restore routes
    for (const route of routes) {
      await db.execute(`
        INSERT INTO routes (route_id, source, destination, distance, base_fare, created_at)
        VALUES (?, ?, ?, ?, ?, ?)
      `, [route.route_id, route.source, route.destination, route.distance, route.base_fare, route.created_at]);
    }
    console.log(`   ✅ Restored ${routes.length} routes`);
    
    // Restore schedules
    for (const schedule of schedules) {
      await db.execute(`
        INSERT INTO bus_schedules (schedule_id, bus_id, route_id, departure_time, arrival_time, fare, available_seats, status, created_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [schedule.schedule_id, schedule.bus_id, schedule.route_id, schedule.departure_time, schedule.arrival_time, schedule.fare, schedule.available_seats, schedule.status, schedule.created_at]);
    }
    console.log(`   ✅ Restored ${schedules.length} schedules`);
    
    // Restore drivers
    for (const driver of drivers) {
      await db.execute(`
        INSERT INTO drivers (driver_id, full_name, license_number, phone, experience_years, status, created_at)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `, [driver.driver_id, driver.full_name, driver.license_number, driver.phone, driver.experience_years, driver.status, driver.created_at]);
    }
    console.log(`   ✅ Restored ${drivers.length} drivers`);
    
    // Restore bus staff
    for (const staff of busStaff) {
      await db.execute(`
        INSERT INTO bus_staff (staff_id, full_name, role, phone, status, created_at)
        VALUES (?, ?, ?, ?, ?, ?)
      `, [staff.staff_id, staff.full_name, staff.role, staff.phone, staff.status, staff.created_at]);
    }
    console.log(`   ✅ Restored ${busStaff.length} bus staff`);
    
    console.log('\n✅ Database rebuild completed successfully!');
    console.log('\n📊 Final table count:');
    const [finalTables] = await db.execute('SHOW TABLES');
    finalTables.forEach((table, index) => {
      console.log(`   ${index + 1}. ${Object.values(table)[0]}`);
    });
    
  } catch (error) {
    console.error('❌ Error rebuilding database:', error);
  } finally {
    process.exit(0);
  }
}

rebuildDatabase();
