const db = require('../db/connection');

async function verifySupriyaDeletion() {
  try {
    console.log('🔍 Verifying deletion of "Supriya" users...\n');
    
    // Check for any remaining users with "<PERSON><PERSON><PERSON>" in their name
    const [remainingUsers] = await db.execute(`
      SELECT user_id, full_name, email, phone, created_at
      FROM users 
      WHERE LOWER(full_name) LIKE LOWER('%supriya%')
    `);

    if (remainingUsers.length > 0) {
      console.log('❌ WARNING: Found remaining users with "Su<PERSON>riya" in their name:');
      remainingUsers.forEach((user, index) => {
        console.log(`${index + 1}. ID: ${user.user_id}, Name: ${user.full_name}, Email: ${user.email}`);
      });
    } else {
      console.log('✅ CONFIRMED: No users with "<PERSON><PERSON>riya" in their name found in the database.');
    }

    // Check for any orphaned bookings that might reference deleted user IDs 3, 9, or 14
    const deletedUserIds = [3, 9, 14];
    console.log('\n🔍 Checking for orphaned bookings...');
    
    for (const userId of deletedUserIds) {
      const [orphanedBookings] = await db.execute(
        'SELECT COUNT(*) as count FROM bookings WHERE user_id = ?',
        [userId]
      );
      
      if (orphanedBookings[0].count > 0) {
        console.log(`❌ WARNING: Found ${orphanedBookings[0].count} orphaned bookings for deleted user ID ${userId}`);
      } else {
        console.log(`✅ No orphaned bookings found for user ID ${userId}`);
      }
    }

    // Check for any orphaned feedback
    console.log('\n🔍 Checking for orphaned feedback...');
    
    for (const userId of deletedUserIds) {
      const [orphanedFeedback] = await db.execute(
        'SELECT COUNT(*) as count FROM feedback WHERE user_id = ?',
        [userId]
      );
      
      if (orphanedFeedback[0].count > 0) {
        console.log(`❌ WARNING: Found ${orphanedFeedback[0].count} orphaned feedback for deleted user ID ${userId}`);
      } else {
        console.log(`✅ No orphaned feedback found for user ID ${userId}`);
      }
    }

    // Get current database statistics
    console.log('\n📊 Current Database Statistics:');
    console.log('='.repeat(40));
    
    const [userCount] = await db.execute('SELECT COUNT(*) as count FROM users');
    const [bookingCount] = await db.execute('SELECT COUNT(*) as count FROM bookings');
    const [feedbackCount] = await db.execute('SELECT COUNT(*) as count FROM feedback');
    
    console.log(`👥 Total users: ${userCount[0].count}`);
    console.log(`📅 Total bookings: ${bookingCount[0].count}`);
    console.log(`💬 Total feedback: ${feedbackCount[0].count}`);

    // List all remaining users
    console.log('\n📋 All remaining users in database:');
    console.log('='.repeat(40));
    
    const [allUsers] = await db.execute(`
      SELECT user_id, full_name, email, 
             (SELECT COUNT(*) FROM bookings WHERE bookings.user_id = users.user_id) as bookings,
             (SELECT COUNT(*) FROM feedback WHERE feedback.user_id = users.user_id) as feedback
      FROM users 
      ORDER BY user_id
    `);

    allUsers.forEach((user, index) => {
      console.log(`${index + 1}. ID: ${user.user_id}, Name: ${user.full_name}, Email: ${user.email}`);
      console.log(`   📊 Bookings: ${user.bookings}, Feedback: ${user.feedback}`);
    });

    console.log('\n✅ Verification complete!');

  } catch (error) {
    console.error('❌ Error during verification:', error);
  } finally {
    process.exit(0);
  }
}

// Run the verification
verifySupriyaDeletion();
