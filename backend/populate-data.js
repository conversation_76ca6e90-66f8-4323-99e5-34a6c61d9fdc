const mysql = require('mysql2/promise');

async function populateData() {
  try {
    // Create connection
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'Supriyasql@6505',
      database: 'bus_reservation_system'
    });

    console.log('Connected to database');

    // Check existing routes
    const [routes] = await connection.execute('SELECT * FROM routes LIMIT 10');
    console.log('Existing routes:', routes);

    // Get today and tomorrow dates
    const today = new Date().toISOString().split('T')[0];
    const tomorrow = new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString().split('T')[0];

    console.log('Today:', today);
    console.log('Tomorrow:', tomorrow);

    // Insert sample buses if they don't exist
    await connection.execute(`
      INSERT IGNORE INTO buses (bus_id, bus_number, bus_type, total_seats, amenities, status) VALUES
      (1, 'MH-01-AB-1234', 'AC Sleeper', 40, 'AC, WiFi, Charging Point', 'Active'),
      (2, 'MH-01-CD-5678', 'Non-AC Seater', 45, 'WiFi, Charging Point', 'Active'),
      (3, 'MH-01-EF-9012', 'AC Seater', 35, 'AC, WiFi, Charging Point', 'Active')
    `);

    // Insert sample drivers if they don't exist
    await connection.execute(`
      INSERT IGNORE INTO drivers (driver_id, full_name, license_number, experience_years) VALUES
      (1, 'Rajesh Kumar', 'DL123456789', 8),
      (2, 'Amit Sharma', 'DL123456790', 12),
      (3, 'Suresh Patel', 'DL123456791', 6)
    `);

    // Find route ID for Mumbai to Delhi
    let routeId = 130; // Mumbai to Delhi route
    const mumbaiDelhiRoute = routes.find(r => r.source === 'Mumbai' && r.destination === 'Delhi');
    if (mumbaiDelhiRoute) {
      routeId = mumbaiDelhiRoute.route_id;
      console.log('Using Mumbai to Delhi route ID:', routeId);
    } else {
      console.log('Mumbai to Delhi route not found, using default route ID:', routeId);
    }

    // Delete existing schedules for today and tomorrow to avoid conflicts
    await connection.execute(`
      DELETE FROM bus_schedules 
      WHERE DATE(departure_time) IN (?, ?)
    `, [today, tomorrow]);

    // Insert schedules for today
    await connection.execute(`
      INSERT INTO bus_schedules (bus_id, route_id, driver_id, departure_time, arrival_time, fare, available_seats) VALUES
      (1, ?, 1, ?, ?, 2000, 35),
      (2, ?, 2, ?, ?, 1800, 40),
      (3, ?, 3, ?, ?, 2200, 30)
    `, [
      routeId, `${today} 06:00:00`, `${today} 20:00:00`,
      routeId, `${today} 14:00:00`, `${tomorrow} 04:00:00`,
      routeId, `${today} 22:00:00`, `${tomorrow} 12:00:00`
    ]);

    // Insert schedules for tomorrow
    await connection.execute(`
      INSERT INTO bus_schedules (bus_id, route_id, driver_id, departure_time, arrival_time, fare, available_seats) VALUES
      (1, ?, 1, ?, ?, 2000, 35),
      (2, ?, 2, ?, ?, 1800, 40),
      (3, ?, 3, ?, ?, 2200, 30)
    `, [
      routeId, `${tomorrow} 06:00:00`, `${tomorrow} 20:00:00`,
      routeId, `${tomorrow} 14:00:00`, `${tomorrow} 23:59:59`,
      routeId, `${tomorrow} 22:00:00`, `${tomorrow} 23:59:59`
    ]);

    // Check what we inserted
    const [schedules] = await connection.execute(`
      SELECT bs.*, r.source, r.destination, b.bus_number
      FROM bus_schedules bs 
      JOIN routes r ON bs.route_id = r.route_id 
      JOIN buses b ON bs.bus_id = b.bus_id
      WHERE DATE(bs.departure_time) IN (?, ?)
      ORDER BY bs.departure_time
    `, [today, tomorrow]);

    console.log('Inserted schedules:', schedules);
    console.log('Total schedules inserted:', schedules.length);

    await connection.end();
    console.log('✅ Data populated successfully!');

  } catch (error) {
    console.error('❌ Error populating data:', error);
  }
}

populateData();
