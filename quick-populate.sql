-- Quick populate with sample data
USE bus_reservation_system;

-- Insert sample buses if they don't exist
INSERT IGNORE INTO buses (bus_id, bus_number, bus_type, total_seats, amenities, status) VALUES
(1, 'MH-01-AB-1234', 'AC Sleeper', 40, 'AC, WiFi, Charging Point', 'Active'),
(2, 'MH-01-CD-5678', 'Non-AC Seater', 45, 'WiFi, Charging Point', 'Active'),
(3, 'MH-01-EF-9012', 'AC Seater', 35, 'AC, WiFi, Charging Point', 'Active');

-- Insert sample drivers if they don't exist
INSERT IGNORE INTO drivers (driver_id, full_name, phone, license_number, experience_years) VALUES
(1, '<PERSON><PERSON>', '**********', 'DL123456789', 8),
(2, 'Amit <PERSON>', '**********', 'DL123456790', 12),
(3, '<PERSON><PERSON>', '**********', 'DL123456791', 6);

-- Insert today's schedules
SET @today = CURDATE();
SET @tomorrow = DATE_ADD(CURDATE(), INTERVAL 1 DAY);

-- Delete old schedules for today and tomorrow to avoid duplicates
DELETE FROM bus_schedules WHERE DATE(departure_time) IN (@today, @tomorrow);

-- Insert fresh schedules for today and tomorrow
INSERT INTO bus_schedules (bus_id, route_id, driver_id, departure_time, arrival_time, fare, available_seats, total_seats) VALUES
-- Today's Mumbai to Delhi schedules (route_id 3 based on existing routes)
(1, 3, 1, CONCAT(@today, ' 06:00:00'), CONCAT(@today, ' 20:00:00'), 2000, 35, 40),
(2, 3, 2, CONCAT(@today, ' 14:00:00'), CONCAT(@tomorrow, ' 04:00:00'), 1800, 40, 45),
(3, 3, 3, CONCAT(@today, ' 22:00:00'), CONCAT(@tomorrow, ' 12:00:00'), 2200, 30, 35),

-- Tomorrow's Mumbai to Delhi schedules
(1, 3, 1, CONCAT(@tomorrow, ' 06:00:00'), CONCAT(@tomorrow, ' 20:00:00'), 2000, 35, 40),
(2, 3, 2, CONCAT(@tomorrow, ' 14:00:00'), DATE_ADD(@tomorrow, INTERVAL 14 HOUR), 1800, 40, 45),
(3, 3, 3, CONCAT(@tomorrow, ' 22:00:00'), DATE_ADD(@tomorrow, INTERVAL 1 DAY) + INTERVAL 12 HOUR, 2200, 30, 35);

-- Show what we inserted
SELECT 'SCHEDULES INSERTED:' as info;
SELECT bs.*, r.source, r.destination 
FROM bus_schedules bs 
JOIN routes r ON bs.route_id = r.route_id 
WHERE DATE(bs.departure_time) IN (@today, @tomorrow)
ORDER BY bs.departure_time;
