import React, { useState } from 'react';
import { apiClient } from '../../config/api';

const ApiTest = () => {
  const [testResult, setTestResult] = useState('');
  const [loading, setLoading] = useState(false);

  const testConnection = async () => {
    setLoading(true);
    setTestResult('Testing connection...');

    try {
      // Test with direct fetch first
      const directResponse = await fetch('http://localhost:8080/api/test');
      const directData = await directResponse.json();
      setTestResult(`✅ Direct fetch successful: ${JSON.stringify(directData)}`);

      // Then test with apiClient
      const response = await apiClient.get('/test');
      setTestResult(prev => prev + `\n✅ ApiClient successful: ${JSON.stringify(response)}`);
    } catch (error) {
      setTestResult(`❌ Connection failed: ${error.message}\nStack: ${error.stack}`);
      console.error('API Test Error:', error);
    } finally {
      setLoading(false);
    }
  };

  const testAdminLogin = async () => {
    setLoading(true);
    setTestResult('Testing admin login...');
    
    try {
      const response = await apiClient.post('/admin/login', {
        username: 'admin',
        password: 'admin123'
      });
      setTestResult(`✅ Admin login successful: ${JSON.stringify(response)}`);
    } catch (error) {
      setTestResult(`❌ Admin login failed: ${error.message}`);
      console.error('Admin Login Test Error:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-6 bg-white rounded-lg shadow-md max-w-2xl mx-auto mt-8">
      <h2 className="text-2xl font-bold mb-4">API Connection Test</h2>
      
      <div className="space-y-4">
        <button
          onClick={testConnection}
          disabled={loading}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
        >
          {loading ? 'Testing...' : 'Test Basic Connection'}
        </button>
        
        <button
          onClick={testAdminLogin}
          disabled={loading}
          className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50 ml-4"
        >
          {loading ? 'Testing...' : 'Test Admin Login'}
        </button>
      </div>
      
      {testResult && (
        <div className="mt-4 p-4 bg-gray-100 rounded">
          <h3 className="font-semibold mb-2">Test Result:</h3>
          <pre className="whitespace-pre-wrap text-sm">{testResult}</pre>
        </div>
      )}
      
      <div className="mt-4 text-sm text-gray-600">
        <p><strong>API Base URL:</strong> {apiClient.baseURL}</p>
        <p><strong>Frontend URL:</strong> {window.location.origin}</p>
      </div>
    </div>
  );
};

export default ApiTest;
